<?php
require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'classes/EmailManager.php';
require_once 'classes/EmailScheduler.php';

// Verificar permissões (gestor e admin podem acessar)
checkPageAccess(['gestor', 'admin']);

$emailManager = new EmailManager();
$emailScheduler = new EmailScheduler();

// Processar ações
$mensagem = '';
$tipo_mensagem = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $acao = $_POST['acao'] ?? '';
        
        switch ($acao) {
            case 'envio_multiplo':
                $template_id = $_POST['template_id'];
                $cpfs_selecionados = explode(',', $_POST['cpfs_selecionados']);
                $cpfs_selecionados = array_filter($cpfs_selecionados); // Remove valores vazios

                if (empty($cpfs_selecionados)) {
                    throw new Exception("Nenhum colaborador selecionado");
                }

                $total_agendados = 0;
                $erros = [];

                foreach ($cpfs_selecionados as $cpf) {
                    try {
                        $emailManager->agendarEnvioIndividual($template_id, trim($cpf));
                        $total_agendados++;
                    } catch (Exception $e) {
                        $erros[] = "CPF $cpf: " . $e->getMessage();
                    }
                }

                if ($total_agendados > 0) {
                    $mensagem = "E-mails agendados com sucesso! Total: $total_agendados";
                    if (!empty($erros)) {
                        $mensagem .= ". Erros: " . count($erros);
                    }
                    $tipo_mensagem = empty($erros) ? "success" : "warning";
                } else {
                    $mensagem = "Nenhum e-mail foi agendado. Erros: " . implode(', ', $erros);
                    $tipo_mensagem = "danger";
                }
                break;

            case 'envio_individual_modal':
                $template_id = $_POST['template_id'];
                $cpf_colaborador = $_POST['cpf_colaborador'];

                if (empty($cpf_colaborador)) {
                    throw new Exception("CPF do colaborador não fornecido");
                }

                try {
                    $emailManager->agendarEnvioIndividual($template_id, trim($cpf_colaborador));
                    $mensagem = "E-mail agendado com sucesso!";
                    $tipo_mensagem = "success";
                } catch (Exception $e) {
                    throw new Exception("Erro ao agendar e-mail: " . $e->getMessage());
                }
                break;

            case 'envio_massa':
                $template_id = $_POST['template_id'];
                $tipo_destinatario = $_POST['tipo_destinatario'];
                $filtros = [
                    'pa' => $_POST['filtro_pa'] ?? '',
                    'funcao' => $_POST['filtro_funcao'] ?? '',
                    'trilha' => $_POST['filtro_trilha'] ?? '',
                    'dias_limite' => $_POST['dias_limite'] ?? 30
                ];
                $cpfs_selecionados = $_POST['cpfs_selecionados'] ?? [];
                
                $total = $emailManager->agendarEnvioMassa($template_id, $tipo_destinatario, $filtros, $cpfs_selecionados);
                $mensagem = "E-mails agendados com sucesso! Total: $total";
                $tipo_mensagem = "success";
                break;
                
            case 'criar_agendamento':
                $dados = [
                    'nome' => $_POST['nome'],
                    'descricao' => $_POST['descricao'],
                    'template_id' => $_POST['template_id'],
                    'tipo_destinatario' => $_POST['tipo_destinatario'],
                    'filtros' => [
                        'pa' => $_POST['filtro_pa'] ?? '',
                        'funcao' => $_POST['filtro_funcao'] ?? '',
                        'trilha' => $_POST['filtro_trilha'] ?? '',
                        'dias_limite' => $_POST['dias_limite'] ?? 30
                    ],
                    'frequencia' => $_POST['frequencia'],
                    'dia_semana' => $_POST['dia_semana'] ?? null,
                    'dia_mes' => $_POST['dia_mes'] ?? null,
                    'hora_envio' => $_POST['hora_envio'],
                    'data_inicio' => $_POST['data_inicio'],
                    'data_fim' => $_POST['data_fim'] ?: null,
                    'ativo' => isset($_POST['ativo'])
                ];
                
                $agendamento_id = $emailScheduler->criarAgendamento($dados);
                $mensagem = "Agendamento criado com sucesso! ID: $agendamento_id";
                $tipo_mensagem = "success";
                break;
                
            case 'processar_fila':
                $resultados = $emailManager->processarFila(20);
                $sucessos = count(array_filter($resultados, fn($r) => $r['status'] === 'sucesso'));
                $erros = count(array_filter($resultados, fn($r) => $r['status'] === 'erro'));
                $mensagem = "Fila processada! Sucessos: $sucessos, Erros: $erros";
                $tipo_mensagem = $erros > 0 ? "warning" : "success";
                break;

            case 'remover_da_fila':
                $fila_id = $_POST['fila_id'];
                $resultado = $emailManager->removerDaFila($fila_id);
                $mensagem = $resultado['message'];
                $tipo_mensagem = $resultado['success'] ? "success" : "danger";
                break;

            case 'limpar_fila_erros':
                $resultado = $emailManager->limparFilaErros();
                $mensagem = $resultado['message'];
                $tipo_mensagem = $resultado['success'] ? "success" : "danger";
                break;

            case 'executar_agendamentos':
                $resultados = $emailScheduler->executarAgendamentos();
                $total = count($resultados);
                $sucessos = count(array_filter($resultados, fn($r) => $r['status'] === 'sucesso'));
                $mensagem = "Execução manual concluída! $sucessos de $total agendamentos executados com sucesso.";
                $tipo_mensagem = $sucessos === $total ? "success" : "warning";
                break;

            case 'alterar_status_agendamento':
                $id = $_POST['agendamento_id'];
                $ativo = $_POST['ativo'] === '1';
                $emailScheduler->alterarStatusAgendamento($id, $ativo);
                $mensagem = "Status do agendamento alterado com sucesso!";
                $tipo_mensagem = "success";
                break;

            case 'remover_agendamento':
                $id = $_POST['agendamento_id'];
                $emailScheduler->removerAgendamento($id);
                $mensagem = "Agendamento removido com sucesso!";
                $tipo_mensagem = "success";
                break;

            case 'salvar_template':
                $template_id = $_POST['template_id'] ?: null;
                $dados = [
                    'nome' => $_POST['nome'],
                    'assunto' => $_POST['assunto'],
                    'corpo_html' => $_POST['corpo_html'],
                    'corpo_texto' => $_POST['corpo_texto'] ?: strip_tags($_POST['corpo_html']),
                    'tipo' => $_POST['tipo'],
                    'ativo' => isset($_POST['ativo']),
                    'variaveis_disponiveis' => json_encode([
                        'nome_colaborador', 'cpf_colaborador', 'email_colaborador',
                        'funcao_colaborador', 'pa_colaborador', 'total_cursos_a_vencer',
                        'total_cursos_vencidos', 'lista_cursos_a_vencer', 'lista_cursos_vencidos',
                        'lista_cursos_a_vencer_texto', 'lista_cursos_vencidos_texto', 'data_envio'
                    ])
                ];

                if ($template_id) {
                    // Atualizar template existente
                    $stmt = $pdo_edu->prepare("
                        UPDATE edu_email_templates
                        SET nome = ?, assunto = ?, corpo_html = ?, corpo_texto = ?,
                            tipo = ?, ativo = ?, variaveis_disponiveis = ?,
                            usuario_atualizacao = ?, data_atualizacao = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([
                        $dados['nome'], $dados['assunto'], $dados['corpo_html'],
                        $dados['corpo_texto'], $dados['tipo'], $dados['ativo'],
                        $dados['variaveis_disponiveis'], $_SESSION['user_id'] ?? 1, $template_id
                    ]);
                    $mensagem = "Template atualizado com sucesso!";
                } else {
                    // Criar novo template
                    $stmt = $pdo_edu->prepare("
                        INSERT INTO edu_email_templates
                        (nome, assunto, corpo_html, corpo_texto, tipo, ativo, variaveis_disponiveis, usuario_criacao)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $dados['nome'], $dados['assunto'], $dados['corpo_html'],
                        $dados['corpo_texto'], $dados['tipo'], $dados['ativo'],
                        $dados['variaveis_disponiveis'], $_SESSION['user_id'] ?? 1
                    ]);
                    $mensagem = "Template criado com sucesso!";
                }
                $tipo_mensagem = "success";
                break;
        }
    } catch (Exception $e) {
        $mensagem = "Erro: " . $e->getMessage();
        $tipo_mensagem = "danger";
    }
}

// Buscar dados para exibição
$templates = $emailManager->listarTemplates();
$agendamentos = $emailScheduler->listarAgendamentos();
$fila_itens = $emailManager->listarFila(100);

// Buscar funções e trilhas para filtros
$stmt = $pdo_edu->query("SELECT DISTINCT funcao FROM edu_relatorio_educacao WHERE funcao IS NOT NULL ORDER BY funcao");
$funcoes = $stmt->fetchAll(PDO::FETCH_COLUMN);

$stmt = $pdo_edu->query("SELECT DISTINCT trilha FROM edu_relatorio_educacao WHERE trilha IS NOT NULL ORDER BY trilha");
$trilhas = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Buscar estatísticas
$stmt = $pdo_edu->prepare("
    SELECT
        COUNT(*) as total_envios,
        SUM(CASE WHEN status = 'enviado' THEN 1 ELSE 0 END) as total_sucessos,
        SUM(CASE WHEN status = 'erro' THEN 1 ELSE 0 END) as total_erros,
        SUM(CASE WHEN status = 'pendente' THEN 1 ELSE 0 END) as total_pendentes
    FROM edu_email_envios
    WHERE DATE(data_criacao) = CURDATE()
");
$stmt->execute();
$stats_hoje = $stmt->fetch(PDO::FETCH_ASSOC);

// Processar filtros de histórico
$filtro_historico = $_GET['filtro_historico'] ?? 'todos';
$where_historico = "1=1";
$params_historico = [];

switch ($filtro_historico) {
    case 'hoje':
        $where_historico = "DATE(e.data_criacao) = CURDATE()";
        break;
    case 'semana':
        $where_historico = "e.data_criacao >= DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
                           AND e.data_criacao < DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 7 DAY)";
        break;
    case 'mes':
        $where_historico = "YEAR(e.data_criacao) = YEAR(CURDATE()) AND MONTH(e.data_criacao) = MONTH(CURDATE())";
        break;
    case 'todos':
    default:
        $where_historico = "1=1";
        break;
}

$stmt = $pdo_edu->prepare("
    SELECT COUNT(*) as fila_pendente
    FROM edu_email_fila
    WHERE status = 'pendente'
");
$stmt->execute();
$fila_stats = $stmt->fetch(PDO::FETCH_ASSOC);

// Buscar estatísticas do período filtrado para o histórico
$stmt_stats_periodo = $pdo_edu->prepare("
    SELECT
        COUNT(*) as total_periodo,
        SUM(CASE WHEN status = 'enviado' THEN 1 ELSE 0 END) as sucessos_periodo,
        SUM(CASE WHEN status = 'erro' THEN 1 ELSE 0 END) as erros_periodo,
        SUM(CASE WHEN status = 'pendente' THEN 1 ELSE 0 END) as pendentes_periodo
    FROM edu_email_envios e
    WHERE $where_historico
");
$stmt_stats_periodo->execute($params_historico);
$stats_periodo = $stmt_stats_periodo->fetch(PDO::FETCH_ASSOC);

// Garantir que stats_periodo sempre tenha valores padrão
if (!$stats_periodo) {
    $stats_periodo = [
        'total_periodo' => 0,
        'sucessos_periodo' => 0,
        'erros_periodo' => 0,
        'pendentes_periodo' => 0
    ];
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciamento de E-mails - Educação Corporativa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #2C5530;
            --sicoob-verde-claro: #7CB342;
            --sicoob-turquesa: #00AE9D;
            --sicoob-amarelo: #C9D200;
            --sicoob-branco: #FFFFFF;
        }



        .card-header-custom {
            background: linear-gradient(135deg, var(--sicoob-turquesa), var(--sicoob-verde-escuro));
            color: white;
        }

        .btn-sicoob {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            border: none;
            color: white;
        }

        .btn-sicoob:hover {
            background: linear-gradient(135deg, var(--sicoob-turquesa), var(--sicoob-verde-escuro));
            color: white;
        }

        .stats-card {
            border-left: 4px solid var(--sicoob-turquesa);
        }

        .nav-pills .nav-link {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 1px solid #dee2e6;
            color: var(--sicoob-verde-escuro);
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .nav-pills .nav-link:hover {
            background: linear-gradient(135deg, var(--sicoob-turquesa), var(--sicoob-verde-claro));
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: white;
            border-color: var(--sicoob-verde-escuro);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(44, 85, 48, 0.3);
        }

        .nav-pills .nav-link.active:hover {
            background: linear-gradient(135deg, var(--sicoob-turquesa), var(--sicoob-verde-escuro));
        }

        .cursor-pointer {
            cursor: pointer;
        }

        .hover-bg-light:hover {
            background-color: #f8f9fa !important;
        }

        .hover-bg-danger:hover {
            background-color: #f8d7da !important;
        }

        .colaborador-item:hover {
            background-color: #e3f2fd;
        }

        .colaborador-selecionado:hover {
            background-color: #ffebee;
        }

        .badge.cursor-pointer:hover {
            opacity: 0.8;
        }

        .table-responsive {
            border-radius: 0.375rem;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .table td {
            vertical-align: middle;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 174, 157, 0.05);
        }

        /* Estilo para botões de filtro de histórico */
        .btn-group .btn-primary {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            border-color: var(--sicoob-turquesa);
            color: white;
        }

        .btn-group .btn-primary:hover {
            background: linear-gradient(135deg, var(--sicoob-turquesa), var(--sicoob-verde-escuro));
            border-color: var(--sicoob-verde-escuro);
        }

        .btn-group .btn-light {
            background-color: #f8f9fa;
            border-color: #dee2e6;
            color: var(--sicoob-verde-escuro);
        }

        .btn-group .btn-light:hover {
            background: linear-gradient(135deg, rgba(0, 174, 157, 0.1), rgba(201, 210, 0, 0.1));
            border-color: var(--sicoob-turquesa);
            color: var(--sicoob-verde-escuro);
        }
    </style>
</head>
<body class="bg-light">
    <?php include 'includes/navbar.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Mensagens -->
        <?php if ($mensagem): ?>
        <div class="alert alert-<?php echo $tipo_mensagem; ?> alert-dismissible fade show" role="alert">
            <i class="fas fa-<?php echo $tipo_mensagem === 'success' ? 'check-circle' : ($tipo_mensagem === 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
            <?php echo htmlspecialchars($mensagem); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Estatísticas -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">Envios Hoje</h6>
                                <h3 class="mb-0"><?php echo number_format($stats_hoje['total_envios']); ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-paper-plane fa-2x" style="color: var(--sicoob-turquesa);"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">Sucessos</h6>
                                <h3 class="mb-0 text-success"><?php echo number_format($stats_hoje['total_sucessos']); ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x" style="color: var(--sicoob-verde-claro);"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">Erros</h6>
                                <h3 class="mb-0 text-danger"><?php echo number_format($stats_hoje['total_erros']); ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">Fila Pendente</h6>
                                <h3 class="mb-0 text-warning"><?php echo number_format($fila_stats['fila_pendente']); ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Abas principais -->
        <div class="card mb-4" style="background: linear-gradient(135deg, rgba(0, 174, 157, 0.05), rgba(124, 179, 66, 0.05)); border: none;">
            <div class="card-body py-3">
                <ul class="nav nav-pills mb-0 justify-content-center" id="emailTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="envio-tab" data-bs-toggle="pill" data-bs-target="#envio" type="button" role="tab">
                    <i class="fas fa-paper-plane me-2"></i>Enviar E-mails
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="agendamentos-tab" data-bs-toggle="pill" data-bs-target="#agendamentos" type="button" role="tab">
                    <i class="fas fa-calendar-alt me-2"></i>Agendamentos
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="templates-tab" data-bs-toggle="pill" data-bs-target="#templates" type="button" role="tab">
                    <i class="fas fa-file-alt me-2"></i>Templates
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="fila-tab" data-bs-toggle="pill" data-bs-target="#fila" type="button" role="tab">
                    <i class="fas fa-list me-2"></i>Gerenciar Fila
                    <?php if (count($fila_itens) > 0): ?>
                    <span class="badge ms-1" style="background-color: var(--sicoob-amarelo); color: var(--sicoob-verde-escuro);"><?php echo count($fila_itens); ?></span>
                    <?php endif; ?>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="historico-tab" data-bs-toggle="pill" data-bs-target="#historico" type="button" role="tab">
                    <i class="fas fa-history me-2"></i>Histórico
                </button>
            </li>
                </ul>
            </div>
        </div>

        <!-- Conteúdo das abas -->
        <div class="tab-content" id="emailTabsContent">
            <!-- Aba Envio -->
            <div class="tab-pane fade show active" id="envio" role="tabpanel">
                <div class="row">
                    <!-- Envio Individual/Múltiplo -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>Envio para Colaboradores
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" id="formEnvioIndividual">
                                    <input type="hidden" name="acao" value="envio_multiplo">

                                    <div class="mb-3">
                                        <label for="template_individual" class="form-label">Template</label>
                                        <select class="form-select" id="template_individual" name="template_id" required>
                                            <option value="">Selecione um template</option>
                                            <?php foreach ($templates as $template): ?>
                                            <option value="<?php echo $template['id']; ?>">
                                                <?php echo htmlspecialchars($template['nome']); ?>
                                                (<?php echo ucfirst($template['tipo']); ?>)
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="busca_colaborador" class="form-label">
                                            Buscar Colaboradores
                                            <small class="text-muted">(digite nome, CPF, e-mail ou função)</small>
                                        </label>
                                        <input type="text" class="form-control" id="busca_colaborador"
                                               placeholder="Digite para buscar colaboradores..." autocomplete="off">
                                    </div>

                                    <!-- Lista de resultados da busca -->
                                    <div class="mb-3" id="resultados_busca" style="display: none;">
                                        <label class="form-label">Resultados da Busca:</label>
                                        <div id="lista_colaboradores" class="border rounded p-2" style="max-height: 200px; overflow-y: auto;">
                                            <!-- Colaboradores aparecerão aqui -->
                                        </div>
                                    </div>

                                    <!-- Colaboradores selecionados -->
                                    <div class="mb-3" id="colaboradores_selecionados_container" style="display: none;">
                                        <label class="form-label">
                                            Colaboradores Selecionados:
                                            <span id="contador_selecionados" class="badge bg-primary">0</span>
                                        </label>
                                        <div id="colaboradores_selecionados" class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                                            <!-- Colaboradores selecionados aparecerão aqui -->
                                        </div>
                                        <small class="text-muted">Clique em um colaborador selecionado para removê-lo da lista.</small>
                                    </div>

                                    <!-- Campo hidden para enviar CPFs selecionados -->
                                    <input type="hidden" name="cpfs_selecionados" id="cpfs_selecionados">

                                    <button type="submit" class="btn btn-sicoob" id="btn_enviar_individual" disabled>
                                        <i class="fas fa-paper-plane me-2"></i>
                                        <span id="texto_botao">Selecione colaboradores</span>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Envio em Massa -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>Envio em Massa
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="acao" value="envio_massa">
                                    
                                    <div class="mb-3">
                                        <label for="template_massa" class="form-label">Template</label>
                                        <select class="form-select" id="template_massa" name="template_id" required>
                                            <option value="">Selecione um template</option>
                                            <?php foreach ($templates as $template): ?>
                                            <option value="<?php echo $template['id']; ?>">
                                                <?php echo htmlspecialchars($template['nome']); ?> 
                                                (<?php echo ucfirst($template['tipo']); ?>)
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="tipo_destinatario" class="form-label">Destinatários</label>
                                        <select class="form-select" id="tipo_destinatario" name="tipo_destinatario" required>
                                            <option value="">Selecione o tipo</option>
                                            <option value="a_vencer">Colaboradores com cursos a vencer</option>
                                            <option value="vencidos">Colaboradores com cursos vencidos</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3" id="filtros_massa" style="display: none;">
                                        <label class="form-label">Filtros (opcional)</label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" name="filtro_pa" placeholder="PA">
                                            </div>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" name="filtro_funcao" placeholder="Função">
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" name="filtro_trilha" placeholder="Trilha">
                                            </div>
                                            <div class="col-md-6" id="dias_limite_container" style="display: none;">
                                                <input type="number" class="form-control" name="dias_limite" placeholder="Dias limite" value="30">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-sicoob">
                                        <i class="fas fa-paper-plane me-2"></i>Enviar E-mails
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Processamento da Fila -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-cogs me-2"></i>Processamento da Fila
                                    </h5>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="acao" value="processar_fila">
                                        <button type="submit" class="btn btn-light btn-sm">
                                            <i class="fas fa-play me-1"></i>Processar Fila
                                        </button>
                                    </form>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">
                                    <i class="fas fa-info-circle me-2 text-info"></i>
                                    Existem <strong><?php echo number_format($fila_stats['fila_pendente']); ?></strong> e-mails pendentes na fila de processamento.
                                    Clique em "Processar Fila" para enviar os e-mails agendados.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba Agendamentos -->
            <div class="tab-pane fade" id="agendamentos" role="tabpanel">
                <div class="row">
                    <!-- Formulário de Novo Agendamento -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <h5 class="mb-0">
                                    <i class="fas fa-plus me-2"></i>Novo Agendamento
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="acao" value="criar_agendamento">

                                    <div class="mb-3">
                                        <label for="nome_agendamento" class="form-label">Nome do Agendamento</label>
                                        <input type="text" class="form-control" id="nome_agendamento" name="nome" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="descricao_agendamento" class="form-label">Descrição</label>
                                        <textarea class="form-control" id="descricao_agendamento" name="descricao" rows="2"></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label for="template_agendamento" class="form-label">Template</label>
                                        <select class="form-select" id="template_agendamento" name="template_id" required>
                                            <option value="">Selecione um template</option>
                                            <?php foreach ($templates as $template): ?>
                                            <option value="<?php echo $template['id']; ?>">
                                                <?php echo htmlspecialchars($template['nome']); ?>
                                                (<?php echo ucfirst($template['tipo']); ?>)
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="tipo_destinatario_agendamento" class="form-label">Destinatários</label>
                                        <select class="form-select" id="tipo_destinatario_agendamento" name="tipo_destinatario" required>
                                            <option value="">Selecione o tipo</option>
                                            <option value="a_vencer">Colaboradores com cursos a vencer</option>
                                            <option value="vencidos">Colaboradores com cursos vencidos</option>
                                        </select>
                                    </div>

                                    <!-- Filtros para agendamentos -->
                                    <div class="mb-3" id="filtros_agendamento" style="display: none;">
                                        <label class="form-label">Filtros (opcional)</label>

                                        <div class="mb-2">
                                            <input type="text" class="form-control form-control-sm" name="filtro_pa" placeholder="PA (ex: 3049)">
                                        </div>

                                        <div class="mb-2">
                                            <select class="form-select form-select-sm" name="filtro_funcao">
                                                <option value="">Todas as funções</option>
                                                <?php foreach ($funcoes as $funcao): ?>
                                                <option value="<?php echo htmlspecialchars($funcao); ?>">
                                                    <?php echo htmlspecialchars($funcao); ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="mb-2">
                                            <select class="form-select form-select-sm" name="filtro_trilha">
                                                <option value="">Todas as trilhas</option>
                                                <?php foreach ($trilhas as $trilha): ?>
                                                <option value="<?php echo htmlspecialchars($trilha); ?>">
                                                    <?php echo htmlspecialchars($trilha); ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div id="dias_limite_container_agendamento" style="display: none;">
                                            <input type="number" class="form-control form-control-sm" name="dias_limite" placeholder="Dias limite" value="30" min="1" max="365">
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="frequencia" class="form-label">Frequência</label>
                                        <select class="form-select" id="frequencia" name="frequencia" required>
                                            <option value="">Selecione a frequência</option>
                                            <option value="unico">Execução única</option>
                                            <option value="diario">Diário</option>
                                            <option value="semanal">Semanal</option>
                                            <option value="quinzenal">Quinzenal</option>
                                            <option value="mensal">Mensal</option>
                                        </select>
                                    </div>

                                    <div class="mb-3" id="dia_semana_container_agendamento" style="display: none;">
                                        <label for="dia_semana" class="form-label">Dia da Semana</label>
                                        <select class="form-select" id="dia_semana" name="dia_semana">
                                            <option value="">Selecione o dia</option>
                                            <option value="1">Segunda-feira</option>
                                            <option value="2">Terça-feira</option>
                                            <option value="3">Quarta-feira</option>
                                            <option value="4">Quinta-feira</option>
                                            <option value="5">Sexta-feira</option>
                                            <option value="6">Sábado</option>
                                            <option value="7">Domingo</option>
                                        </select>
                                    </div>

                                    <div class="mb-3" id="dia_mes_container_agendamento" style="display: none;">
                                        <label for="dia_mes" class="form-label">Dia do Mês</label>
                                        <select class="form-select" id="dia_mes" name="dia_mes">
                                            <option value="">Selecione o dia</option>
                                            <?php for ($i = 1; $i <= 31; $i++): ?>
                                            <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="hora_envio" class="form-label">Hora do Envio</label>
                                        <input type="time" class="form-control" id="hora_envio" name="hora_envio" value="09:00" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="data_inicio" class="form-label">Data de Início</label>
                                        <input type="date" class="form-control" id="data_inicio" name="data_inicio"
                                               value="<?php echo date('Y-m-d'); ?>" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="data_fim" class="form-label">Data de Fim (opcional)</label>
                                        <input type="date" class="form-control" id="data_fim" name="data_fim">
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="ativo_agendamento" name="ativo" checked>
                                            <label class="form-check-label" for="ativo_agendamento">
                                                Ativo
                                            </label>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn btn-sicoob w-100">
                                        <i class="fas fa-save me-2"></i>Criar Agendamento
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Lista de Agendamentos -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-list me-2"></i>Agendamentos Existentes
                                    </h5>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="acao" value="executar_agendamentos">
                                        <button type="submit" class="btn btn-light btn-sm" onclick="return confirm('Executar todos os agendamentos que estão na hora?')">
                                            <i class="fas fa-play me-1"></i>Executar Agora
                                        </button>
                                    </form>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <?php if (empty($agendamentos)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Nenhum agendamento encontrado</h5>
                                    <p class="text-muted">Crie seu primeiro agendamento usando o formulário ao lado.</p>
                                </div>
                                <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>Nome</th>
                                                <th>Template</th>
                                                <th>Destinatários</th>
                                                <th>Frequência</th>
                                                <th>Próxima Execução</th>
                                                <th>Status</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($agendamentos as $agendamento): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($agendamento['nome']); ?></strong>
                                                    <?php if ($agendamento['descricao']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($agendamento['descricao']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge" style="background-color: var(--sicoob-turquesa); color: white;">
                                                        <?php echo htmlspecialchars($agendamento['template_nome']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge" style="background-color: var(--sicoob-verde-escuro); color: white;">
                                                        <?php echo ucfirst(str_replace('_', ' ', $agendamento['tipo_destinatario'])); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo ucfirst($agendamento['frequencia']); ?>
                                                    <?php if ($agendamento['dia_semana']): ?>
                                                    <br><small class="text-muted">
                                                        <?php
                                                        $dias = ['', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'];
                                                        echo $dias[$agendamento['dia_semana']];
                                                        ?>
                                                    </small>
                                                    <?php endif; ?>
                                                    <?php if ($agendamento['dia_mes']): ?>
                                                    <br><small class="text-muted">Dia <?php echo $agendamento['dia_mes']; ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($agendamento['proxima_execucao']): ?>
                                                    <small>
                                                        <?php echo date('d/m/Y H:i', strtotime($agendamento['proxima_execucao'])); ?>
                                                    </small>
                                                    <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $agendamento['ativo'] ? 'success' : 'secondary'; ?>">
                                                        <?php echo $agendamento['ativo'] ? 'Ativo' : 'Inativo'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <!-- Toggle Status -->
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="acao" value="alterar_status_agendamento">
                                                            <input type="hidden" name="agendamento_id" value="<?php echo $agendamento['id']; ?>">
                                                            <input type="hidden" name="ativo" value="<?php echo $agendamento['ativo'] ? '0' : '1'; ?>">
                                                            <button type="submit" class="btn btn-outline-<?php echo $agendamento['ativo'] ? 'warning' : 'success'; ?>"
                                                                    title="<?php echo $agendamento['ativo'] ? 'Desativar' : 'Ativar'; ?>">
                                                                <i class="fas fa-<?php echo $agendamento['ativo'] ? 'pause' : 'play'; ?>"></i>
                                                            </button>
                                                        </form>

                                                        <!-- Remover -->
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="acao" value="remover_agendamento">
                                                            <input type="hidden" name="agendamento_id" value="<?php echo $agendamento['id']; ?>">
                                                            <button type="submit" class="btn btn-outline-danger"
                                                                    onclick="return confirm('Tem certeza que deseja remover este agendamento?')"
                                                                    title="Remover">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba Templates -->
            <div class="tab-pane fade" id="templates" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-file-alt me-2"></i>Templates de E-mail
                                    </h5>
                                    <button class="btn btn-light btn-sm" onclick="novoTemplate()">
                                        <i class="fas fa-plus me-1"></i>Novo Template
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <?php if (empty($templates)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Nenhum template encontrado</h5>
                                    <p class="text-muted">Os templates padrão devem ter sido criados durante a instalação.</p>
                                </div>
                                <?php else: ?>
                                <div class="row">
                                    <?php foreach ($templates as $template): ?>
                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-envelope me-2"></i>
                                                    <?php echo htmlspecialchars($template['nome']); ?>
                                                </h6>
                                                <span class="badge" style="background-color: <?php echo $template['tipo'] === 'a_vencer' ? 'var(--sicoob-amarelo)' : ($template['tipo'] === 'vencidos' ? 'var(--sicoob-vermelho)' : 'var(--sicoob-turquesa)'); ?>; color: <?php echo $template['tipo'] === 'a_vencer' ? 'var(--sicoob-verde-escuro)' : 'white'; ?>;">
                                                    <?php echo ucfirst(str_replace('_', ' ', $template['tipo'])); ?>
                                                </span>
                                            </div>
                                            <div class="card-body">
                                                <p class="card-text">
                                                    <strong>Assunto:</strong><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($template['assunto']); ?></small>
                                                </p>

                                                <p class="card-text">
                                                    <strong>Variáveis disponíveis:</strong><br>
                                                    <?php
                                                    $variaveis = json_decode($template['variaveis_disponiveis'], true);
                                                    if ($variaveis):
                                                    ?>
                                                    <small class="text-muted">
                                                        <?php echo implode(', ', array_map(fn($v) => "{{$v}}", $variaveis)); ?>
                                                    </small>
                                                    <?php endif; ?>
                                                </p>

                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        Criado em <?php echo date('d/m/Y', strtotime($template['data_criacao'])); ?>
                                                    </small>
                                                    <span class="badge bg-<?php echo $template['ativo'] ? 'success' : 'secondary'; ?>">
                                                        <?php echo $template['ativo'] ? 'Ativo' : 'Inativo'; ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="card-footer">
                                                <div class="btn-group w-100" role="group">
                                                    <button class="btn btn-outline-primary btn-sm" onclick="visualizarTemplate(<?php echo $template['id']; ?>)">
                                                        <i class="fas fa-eye me-1"></i>Ver
                                                    </button>
                                                    <button class="btn btn-outline-success btn-sm" onclick="editarTemplate(<?php echo $template['id']; ?>)">
                                                        <i class="fas fa-edit me-1"></i>Editar
                                                    </button>
                                                    <button class="btn btn-outline-info btn-sm" onclick="testarTemplate(<?php echo $template['id']; ?>)">
                                                        <i class="fas fa-paper-plane me-1"></i>Testar
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba Gerenciar Fila -->
            <div class="tab-pane fade" id="fila" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-list me-2"></i>Gerenciar Fila de E-mails
                                        <span class="badge bg-light text-dark ms-2"><?php echo count($fila_itens); ?> itens</span>
                                    </h5>
                                    <div>
                                        <?php if (count(array_filter($fila_itens, fn($item) => $item['status_fila'] === 'erro' || $item['tentativas'] >= $item['max_tentativas'])) > 0): ?>
                                        <form method="POST" class="d-inline me-2" onsubmit="return confirm('Tem certeza que deseja remover todos os itens com erro da fila?')">
                                            <input type="hidden" name="acao" value="limpar_fila_erros">
                                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                                <i class="fas fa-trash me-1"></i>Limpar Erros
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-outline-light btn-sm" onclick="location.reload()">
                                            <i class="fas fa-sync me-1"></i>Atualizar
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <?php if (empty($fila_itens)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Fila vazia</h5>
                                    <p class="text-muted">Não há e-mails pendentes na fila de processamento.</p>
                                </div>
                                <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Status</th>
                                                <th>Destinatário</th>
                                                <th>Template</th>
                                                <th>Agendado para</th>
                                                <th>Tentativas</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($fila_itens as $item): ?>
                                            <tr>
                                                <td>
                                                    <?php
                                                    $status_colors = [
                                                        'pendente' => 'var(--sicoob-amarelo)',
                                                        'processando' => 'var(--sicoob-turquesa)',
                                                        'erro' => 'var(--sicoob-vermelho)'
                                                    ];
                                                    $status_text_colors = [
                                                        'pendente' => 'var(--sicoob-verde-escuro)',
                                                        'processando' => 'white',
                                                        'erro' => 'white'
                                                    ];
                                                    $status_icon = [
                                                        'pendente' => 'clock',
                                                        'processando' => 'spinner',
                                                        'erro' => 'exclamation-triangle'
                                                    ];
                                                    ?>
                                                    <span class="badge" style="background-color: <?php echo $status_colors[$item['status_fila']]; ?>; color: <?php echo $status_text_colors[$item['status_fila']]; ?>;">
                                                        <i class="fas fa-<?php echo $status_icon[$item['status_fila']]; ?> me-1"></i>
                                                        <?php echo ucfirst($item['status_fila']); ?>
                                                    </span>
                                                    <?php if ($item['prioridade'] < 5): ?>
                                                    <span class="badge bg-secondary ms-1" title="Alta prioridade">
                                                        <i class="fas fa-arrow-up"></i>
                                                    </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($item['destinatario_nome']); ?></strong>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($item['destinatario_email']); ?></small>
                                                        <br><small class="text-muted">CPF: <?php echo htmlspecialchars($item['destinatario_cpf']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($item['template_nome']); ?></strong>
                                                        <br><small class="text-muted"><?php echo ucfirst($item['template_tipo']); ?></small>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($item['assunto']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <?php echo date('d/m/Y H:i', strtotime($item['data_agendamento'])); ?>
                                                        <br><small class="text-muted">
                                                            Criado: <?php echo date('d/m/Y H:i', strtotime($item['data_criacao'])); ?>
                                                        </small>
                                                        <?php if ($item['data_processamento']): ?>
                                                        <br><small class="text-muted">
                                                            Processado: <?php echo date('d/m/Y H:i', strtotime($item['data_processamento'])); ?>
                                                        </small>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge" style="background-color: <?php echo $item['tentativas'] >= $item['max_tentativas'] ? 'var(--sicoob-vermelho)' : 'var(--sicoob-cinza-escuro)'; ?>; color: white;">
                                                        <?php echo $item['tentativas']; ?>/<?php echo $item['max_tentativas']; ?>
                                                    </span>
                                                    <?php if ($item['erro_detalhes']): ?>
                                                    <br><small class="text-danger" title="<?php echo htmlspecialchars($item['erro_detalhes']); ?>">
                                                        <i class="fas fa-exclamation-circle"></i> Erro
                                                    </small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <form method="POST" class="d-inline" onsubmit="return confirm('Tem certeza que deseja remover este e-mail da fila?')">
                                                        <input type="hidden" name="acao" value="remover_da_fila">
                                                        <input type="hidden" name="fila_id" value="<?php echo $item['fila_id']; ?>">
                                                        <button type="submit" class="btn btn-outline-danger btn-sm" title="Remover da fila">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <?php if (count($fila_itens) >= 100): ?>
                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Mostrando os primeiros 100 itens da fila. Para ver todos os itens, use filtros ou processe a fila.
                                </div>
                                <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba Histórico -->
            <div class="tab-pane fade" id="historico" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-history me-2"></i>Histórico de Envios
                                        <small class="text-muted">
                                            (<?php echo number_format($stats_periodo['total_periodo']); ?> envios no período)
                                        </small>
                                    </h5>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn <?php echo $filtro_historico === 'hoje' ? 'btn-primary' : 'btn-light'; ?>"
                                                onclick="filtrarHistorico('hoje')">Hoje</button>
                                        <button class="btn <?php echo $filtro_historico === 'semana' ? 'btn-primary' : 'btn-light'; ?>"
                                                onclick="filtrarHistorico('semana')">Esta Semana</button>
                                        <button class="btn <?php echo $filtro_historico === 'mes' ? 'btn-primary' : 'btn-light'; ?>"
                                                onclick="filtrarHistorico('mes')">Este Mês</button>
                                        <button class="btn <?php echo $filtro_historico === 'todos' ? 'btn-primary' : 'btn-light'; ?>"
                                                onclick="filtrarHistorico('todos')">Todos</button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <?php
                                // Buscar histórico de envios com filtros
                                $stmt = $pdo_edu->prepare("
                                    SELECT e.*, t.nome as template_nome, t.tipo as template_tipo
                                    FROM edu_email_envios e
                                    LEFT JOIN edu_email_templates t ON e.template_id = t.id
                                    WHERE $where_historico
                                    ORDER BY e.data_criacao DESC
                                    LIMIT 100
                                ");
                                $stmt->execute($params_historico);
                                $historico = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                ?>

                                <!-- Estatísticas do período -->
                                <?php if ($stats_periodo['total_periodo'] > 0): ?>
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <div class="card border-0" style="background: linear-gradient(135deg, rgba(0, 174, 157, 0.1), rgba(0, 174, 157, 0.05));">
                                            <div class="card-body text-center py-2">
                                                <h6 class="mb-1 text-success"><?php echo number_format($stats_periodo['sucessos_periodo']); ?></h6>
                                                <small class="text-muted">Sucessos</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card border-0" style="background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));">
                                            <div class="card-body text-center py-2">
                                                <h6 class="mb-1 text-danger"><?php echo number_format($stats_periodo['erros_periodo']); ?></h6>
                                                <small class="text-muted">Erros</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card border-0" style="background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));">
                                            <div class="card-body text-center py-2">
                                                <h6 class="mb-1 text-warning"><?php echo number_format($stats_periodo['pendentes_periodo']); ?></h6>
                                                <small class="text-muted">Pendentes</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card border-0" style="background: linear-gradient(135deg, rgba(108, 117, 125, 0.1), rgba(108, 117, 125, 0.05));">
                                            <div class="card-body text-center py-2">
                                                <h6 class="mb-1">
                                                    <?php
                                                    $taxa_sucesso = $stats_periodo['total_periodo'] > 0 ?
                                                        ($stats_periodo['sucessos_periodo'] / $stats_periodo['total_periodo']) * 100 : 0;
                                                    echo number_format($taxa_sucesso, 1) . '%';
                                                    ?>
                                                </h6>
                                                <small class="text-muted">Taxa de Sucesso</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if (empty($historico)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Nenhum envio encontrado</h5>
                                    <p class="text-muted">
                                        <?php if ($filtro_historico !== 'todos'): ?>
                                            Nenhum envio encontrado para o período selecionado.
                                        <?php else: ?>
                                            O histórico aparecerá aqui após os primeiros envios de e-mail.
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Data/Hora</th>
                                                <th>Destinatário</th>
                                                <th>Template</th>
                                                <th>Assunto</th>
                                                <th>Tipo</th>
                                                <th>Status</th>
                                                <th>Tentativas</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($historico as $envio): ?>
                                            <tr>
                                                <td>
                                                    <small>
                                                        <?php echo date('d/m/Y H:i', strtotime($envio['data_criacao'])); ?>
                                                        <?php if ($envio['data_envio']): ?>
                                                        <br><span class="text-muted">Enviado: <?php echo date('d/m/Y H:i', strtotime($envio['data_envio'])); ?></span>
                                                        <?php endif; ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($envio['destinatario_nome']); ?></strong>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($envio['destinatario_email']); ?></small>
                                                    <br><small class="text-muted">CPF: <?php echo htmlspecialchars($envio['destinatario_cpf']); ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge" style="background-color: var(--sicoob-turquesa); color: white;">
                                                        <?php echo htmlspecialchars($envio['template_nome']); ?>
                                                    </span>
                                                    <br><small class="text-muted"><?php echo ucfirst($envio['template_tipo']); ?></small>
                                                </td>
                                                <td>
                                                    <small><?php echo htmlspecialchars($envio['assunto']); ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge" style="background-color: <?php echo $envio['tipo_envio'] === 'manual' ? 'var(--sicoob-verde-escuro)' : 'var(--sicoob-cinza-escuro)'; ?>; color: white;">
                                                        <?php echo ucfirst($envio['tipo_envio']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge" style="background-color: <?php
                                                        echo $envio['status'] === 'enviado' ? 'var(--sicoob-verde-claro)' :
                                                             ($envio['status'] === 'erro' ? 'var(--sicoob-vermelho)' :
                                                              ($envio['status'] === 'pendente' ? 'var(--sicoob-amarelo)' : 'var(--sicoob-cinza-escuro)'));
                                                    ?>; color: <?php echo $envio['status'] === 'pendente' ? 'var(--sicoob-verde-escuro)' : 'white'; ?>;">
                                                        <?php echo ucfirst($envio['status']); ?>
                                                    </span>
                                                    <?php if ($envio['erro_detalhes']): ?>
                                                    <br><small class="text-danger" title="<?php echo htmlspecialchars($envio['erro_detalhes']); ?>">
                                                        <i class="fas fa-exclamation-triangle"></i> Ver erro
                                                    </small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-light text-dark">
                                                        <?php echo $envio['tentativas']; ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Mostrando os últimos 100 envios. Para ver mais, use os filtros acima.
                                    </small>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para visualização de template -->
    <div class="modal fade" id="templateModal" tabindex="-1" aria-labelledby="templateModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="templateModalLabel">
                        <i class="fas fa-eye me-2"></i>Visualizar Template
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="templateContent">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Carregando...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para criar/editar template -->
    <div class="modal fade" id="templateEditorModal" tabindex="-1" aria-labelledby="templateEditorModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="templateEditorModalLabel">
                        <i class="fas fa-edit me-2"></i>Editar Template
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" id="formTemplate">
                    <input type="hidden" name="acao" value="salvar_template">
                    <input type="hidden" name="template_id" id="template_id_edit">

                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nome_template" class="form-label">Nome do Template</label>
                                    <input type="text" class="form-control" id="nome_template" name="nome" required>
                                </div>

                                <div class="mb-3">
                                    <label for="tipo_template" class="form-label">Tipo</label>
                                    <select class="form-select" id="tipo_template" name="tipo" required>
                                        <option value="">Selecione o tipo</option>
                                        <option value="a_vencer">Cursos A Vencer</option>
                                        <option value="vencidos">Cursos Vencidos</option>
                                        <option value="personalizado">Personalizado</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="assunto_template" class="form-label">Assunto</label>
                                    <input type="text" class="form-control" id="assunto_template" name="assunto" required>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="ativo_template" name="ativo" checked>
                                        <label class="form-check-label" for="ativo_template">
                                            Template Ativo
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Variáveis Disponíveis:</label>
                                    <div class="border rounded p-2 bg-light">
                                        <small class="text-muted">
                                            <strong>Clique para inserir:</strong><br>
                                            <strong>Dados do Colaborador:</strong><br>
                                            <span class="badge bg-secondary me-1 mb-1 cursor-pointer" onclick="inserirVariavel('nome_colaborador')">{{nome_colaborador}}</span>
                                            <span class="badge bg-secondary me-1 mb-1 cursor-pointer" onclick="inserirVariavel('cpf_colaborador')">{{cpf_colaborador}}</span>
                                            <span class="badge bg-secondary me-1 mb-1 cursor-pointer" onclick="inserirVariavel('email_colaborador')">{{email_colaborador}}</span>
                                            <span class="badge bg-secondary me-1 mb-1 cursor-pointer" onclick="inserirVariavel('funcao_colaborador')">{{funcao_colaborador}}</span>
                                            <span class="badge bg-secondary me-1 mb-1 cursor-pointer" onclick="inserirVariavel('pa_colaborador')">{{pa_colaborador}}</span>
                                            <br><strong>Totais:</strong><br>
                                            <span class="badge bg-warning me-1 mb-1 cursor-pointer" onclick="inserirVariavel('total_cursos_a_vencer')">{{total_cursos_a_vencer}}</span>
                                            <span class="badge bg-danger me-1 mb-1 cursor-pointer" onclick="inserirVariavel('total_cursos_vencidos')">{{total_cursos_vencidos}}</span>
                                            <br><strong>Listas Detalhadas (HTML):</strong><br>
                                            <span class="badge bg-warning me-1 mb-1 cursor-pointer" onclick="inserirVariavel('lista_cursos_a_vencer')">{{lista_cursos_a_vencer}}</span>
                                            <span class="badge bg-danger me-1 mb-1 cursor-pointer" onclick="inserirVariavel('lista_cursos_vencidos')">{{lista_cursos_vencidos}}</span>
                                            <br><strong>Listas Detalhadas (Texto):</strong><br>
                                            <span class="badge bg-info me-1 mb-1 cursor-pointer" onclick="inserirVariavel('lista_cursos_a_vencer_texto')">{{lista_cursos_a_vencer_texto}}</span>
                                            <span class="badge bg-info me-1 mb-1 cursor-pointer" onclick="inserirVariavel('lista_cursos_vencidos_texto')">{{lista_cursos_vencidos_texto}}</span>
                                            <br><strong>Sistema:</strong><br>
                                            <span class="badge bg-secondary me-1 mb-1 cursor-pointer" onclick="inserirVariavel('data_envio')">{{data_envio}}</span>
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="corpo_html_template" class="form-label">Conteúdo HTML</label>
                                    <textarea class="form-control" id="corpo_html_template" name="corpo_html" rows="15" required></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="corpo_texto_template" class="form-label">Conteúdo Texto (fallback)</label>
                                    <textarea class="form-control" id="corpo_texto_template" name="corpo_texto" rows="8"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-eye me-2"></i>Preview
                                            <button type="button" class="btn btn-sm btn-outline-primary float-end" onclick="atualizarPreview()">
                                                <i class="fas fa-refresh me-1"></i>Atualizar Preview
                                            </button>
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="preview_template" style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                                            <p class="text-muted">Digite o conteúdo HTML e clique em "Atualizar Preview" para ver o resultado.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-sicoob">
                            <i class="fas fa-save me-2"></i>Salvar Template
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>

        // Mostrar filtros quando selecionar tipo de destinatário
        document.getElementById('tipo_destinatario').addEventListener('change', function() {
            const filtros = document.getElementById('filtros_massa');
            const diasLimite = document.getElementById('dias_limite_container');

            if (this.value) {
                filtros.style.display = 'block';
                if (this.value === 'a_vencer') {
                    diasLimite.style.display = 'block';
                } else {
                    diasLimite.style.display = 'none';
                }
            } else {
                filtros.style.display = 'none';
            }
        });

        // Controles para agendamentos - tipo de destinatário
        document.getElementById('tipo_destinatario_agendamento').addEventListener('change', function() {
            const filtros = document.getElementById('filtros_agendamento');
            const diasLimite = document.getElementById('dias_limite_container_agendamento');

            if (this.value) {
                filtros.style.display = 'block';
                if (this.value === 'a_vencer') {
                    diasLimite.style.display = 'block';
                } else {
                    diasLimite.style.display = 'none';
                }
            } else {
                filtros.style.display = 'none';
            }
        });

        // Controles para agendamentos - frequência
        document.getElementById('frequencia').addEventListener('change', function() {
            const diaSemana = document.getElementById('dia_semana_container_agendamento');
            const diaMes = document.getElementById('dia_mes_container_agendamento');

            // Ocultar todos primeiro
            diaSemana.style.display = 'none';
            diaMes.style.display = 'none';

            // Mostrar campos específicos
            if (this.value === 'semanal') {
                diaSemana.style.display = 'block';
            } else if (this.value === 'mensal') {
                diaMes.style.display = 'block';
            }
        });

        // Funções para templates
            window.visualizarTemplate = function(templateId) {
                const modal = new bootstrap.Modal(document.getElementById('templateModal'));
                const content = document.getElementById('templateContent');

                // Mostrar loading
                content.innerHTML = `
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Carregando...</span>
                        </div>
                    </div>
                `;

                modal.show();

                // Buscar template via AJAX
                fetch(`ajax_template.php?id=${templateId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            content.innerHTML = `
                                <div class="mb-3">
                                    <h6><i class="fas fa-envelope me-2"></i>Assunto:</h6>
                                    <div class="alert alert-info">${data.exemplo.assunto}</div>
                                </div>
                                <div class="mb-3">
                                    <h6><i class="fas fa-code me-2"></i>Código HTML:</h6>
                                    <div class="border p-3 bg-light" style="max-height: 200px; overflow-y: auto;">
                                        <pre><code>${data.template.corpo_html.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <h6><i class="fas fa-eye me-2"></i>Preview com Dados de Exemplo:</h6>
                                    <div class="border p-3" style="max-height: 400px; overflow-y: auto;">
                                        <iframe srcdoc="${data.exemplo.corpo_html.replace(/"/g, '&quot;')}"
                                                style="width: 100%; height: 350px; border: none;"></iframe>
                                    </div>
                                </div>
                                <div class="alert alert-warning">
                                    <small><i class="fas fa-info-circle me-1"></i>
                                    <strong>Dados de exemplo utilizados:</strong> João Silva Santos, CPF 123.456.789-01,
                                    função Gerente de Relacionamento, PA 3049-PA EXEMPLO, 3 cursos a vencer, 1 curso vencido.
                                    </small>
                                </div>
                            `;
                        } else {
                            content.innerHTML = `
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Erro ao carregar template: ${data.error}
                                </div>
                            `;
                        }
                    })
                    .catch(error => {
                        content.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Erro ao carregar template: ${error.message}
                            </div>
                        `;
                    });
            };

            window.testarTemplate = function(templateId) {
                const cpf = prompt('Digite o CPF do colaborador para teste:');
                if (cpf) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.innerHTML = `
                        <input type="hidden" name="acao" value="envio_multiplo">
                        <input type="hidden" name="template_id" value="${templateId}">
                        <input type="hidden" name="cpfs_selecionados" value="${cpf}">
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            };

            // Funções para histórico
            window.filtrarHistorico = function(periodo) {
                // Construir URL com filtro
                const url = new URL(window.location);
                url.searchParams.set('filtro_historico', periodo);

                // Manter a aba ativa
                url.hash = '#historico';

                // Redirecionar para aplicar o filtro
                window.location.href = url.toString();
            };

            // Atualização automática da fila (opcional)
            let filaUpdateInterval;

            function iniciarAtualizacaoFila() {
                // Atualizar a cada 30 segundos quando a aba da fila estiver ativa
                filaUpdateInterval = setInterval(function() {
                    const filaTab = document.getElementById('fila-tab');
                    if (filaTab && filaTab.classList.contains('active')) {
                        atualizarStatusFila();
                    }
                }, 30000);
            }

            function pararAtualizacaoFila() {
                if (filaUpdateInterval) {
                    clearInterval(filaUpdateInterval);
                }
            }

            function atualizarStatusFila() {
                fetch('ajax_fila_status.php')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Atualizar badge na aba
                            const filaTab = document.getElementById('fila-tab');
                            const badge = filaTab.querySelector('.badge');
                            if (badge) {
                                badge.textContent = data.stats.total;
                                if (data.stats.total === 0) {
                                    badge.style.display = 'none';
                                } else {
                                    badge.style.display = 'inline';
                                }
                            }
                        }
                    })
                    .catch(error => {
                        console.log('Erro ao atualizar status da fila:', error);
                    });
            }

            // Iniciar atualização quando a página carregar
            iniciarAtualizacaoFila();

            // Parar atualização quando sair da página
            window.addEventListener('beforeunload', pararAtualizacaoFila);

        // Variáveis globais
        let colaboradoresSelecionados = [];
        let timeoutBusca;
        let buscaInicializada = false;

        // Função global para buscar colaboradores
        function buscarColaboradores(termo) {
            const resultadosDiv = document.getElementById('resultados_busca');
            const listaDiv = document.getElementById('lista_colaboradores');

            if (!resultadosDiv || !listaDiv) {
                return;
            }

            listaDiv.innerHTML = '<div class="text-center py-2"><div class="spinner-border spinner-border-sm" role="status"></div> Buscando...</div>';
            resultadosDiv.style.display = 'block';

            fetch(`ajax_colaboradores.php?search=${encodeURIComponent(termo)}&limit=20`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        if (data.colaboradores.length === 0) {
                            listaDiv.innerHTML = '<div class="text-muted text-center py-2">Nenhum colaborador encontrado</div>';
                        } else {
                            listaDiv.innerHTML = data.colaboradores.map(colaborador => `
                                <div class="colaborador-item p-2 border-bottom cursor-pointer hover-bg-light"
                                     onclick="selecionarColaborador('${colaborador.cpf}', '${colaborador.nome}', '${colaborador.email}', '${colaborador.funcao}', '${colaborador.pa}', ${colaborador.cursos_a_vencer}, ${colaborador.cursos_vencidos})">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <strong>${colaborador.nome}</strong><br>
                                            <small class="text-muted">CPF: ${colaborador.cpf_formatado}</small><br>
                                            <small class="text-muted">${colaborador.email}</small><br>
                                            <small class="text-muted">${colaborador.funcao} - ${colaborador.pa}</small>
                                        </div>
                                        <div class="text-end">
                                            ${colaborador.cursos_a_vencer > 0 ? `<span class="badge" style="background-color: var(--sicoob-amarelo); color: var(--sicoob-verde-escuro);">${colaborador.cursos_a_vencer} a vencer</span><br>` : ''}
                                            ${colaborador.cursos_vencidos > 0 ? `<span class="badge" style="background-color: var(--sicoob-vermelho); color: white;">${colaborador.cursos_vencidos} vencidos</span>` : ''}
                                        </div>
                                    </div>
                                </div>
                            `).join('');
                        }
                    } else {
                        listaDiv.innerHTML = `<div class="text-danger text-center py-2">Erro: ${data.error}</div>`;
                    }
                })
                .catch(error => {
                    listaDiv.innerHTML = `<div class="text-danger text-center py-2">Erro na busca: ${error.message}</div>`;
                });
        }

        // Função para inicializar a busca de colaboradores
        function inicializarBuscaColaboradores() {
            if (buscaInicializada) return;

            const campoBusca = document.getElementById('busca_colaborador');

            if (campoBusca) {
                campoBusca.addEventListener('input', function() {
                    const termo = this.value.trim();

                    clearTimeout(timeoutBusca);

                    if (termo.length < 2) {
                        document.getElementById('resultados_busca').style.display = 'none';
                        return;
                    }

                    timeoutBusca = setTimeout(() => {
                        buscarColaboradores(termo);
                    }, 300);
                });
                buscaInicializada = true;
            }
        }

        // Aguardar carregamento completo do DOM
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializar busca imediatamente (aba ativa por padrão)
            inicializarBuscaColaboradores();

            // Também inicializar quando a aba de envio for mostrada
            const envioTab = document.getElementById('envio-tab');
            if (envioTab) {
                envioTab.addEventListener('shown.bs.tab', function() {
                    setTimeout(inicializarBuscaColaboradores, 100);
                });
            }



            // Tornar funções globais para serem acessíveis pelos onclick
            window.selecionarColaborador = function(cpf, nome, email, funcao, pa, cursosAVencer, cursosVencidos) {
                // Verificar se já está selecionado
                if (colaboradoresSelecionados.find(c => c.cpf === cpf)) {
                    alert('Este colaborador já está selecionado!');
                    return;
                }

                const colaborador = { cpf, nome, email, funcao, pa, cursosAVencer, cursosVencidos };
                colaboradoresSelecionados.push(colaborador);

                atualizarListaSelecionados();

                // Limpar busca
                document.getElementById('busca_colaborador').value = '';
                document.getElementById('resultados_busca').style.display = 'none';
            };

            window.removerColaborador = function(cpf) {
                colaboradoresSelecionados = colaboradoresSelecionados.filter(c => c.cpf !== cpf);
                atualizarListaSelecionados();
            };

        function atualizarListaSelecionados() {
            const container = document.getElementById('colaboradores_selecionados_container');
            const lista = document.getElementById('colaboradores_selecionados');
            const contador = document.getElementById('contador_selecionados');
            const botao = document.getElementById('btn_enviar_individual');
            const textoBotao = document.getElementById('texto_botao');
            const cpfsInput = document.getElementById('cpfs_selecionados');

            if (colaboradoresSelecionados.length === 0) {
                container.style.display = 'none';
                botao.disabled = true;
                textoBotao.textContent = 'Selecione colaboradores';
                cpfsInput.value = '';
            } else {
                container.style.display = 'block';
                contador.textContent = colaboradoresSelecionados.length;

                lista.innerHTML = colaboradoresSelecionados.map(colaborador => `
                    <div class="colaborador-selecionado p-2 border-bottom cursor-pointer hover-bg-danger"
                         onclick="removerColaborador('${colaborador.cpf}')" title="Clique para remover">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong>${colaborador.nome}</strong><br>
                                <small class="text-muted">CPF: ${colaborador.cpf}</small><br>
                                <small class="text-muted">${colaborador.email}</small>
                            </div>
                            <div class="text-end">
                                <i class="fas fa-times text-danger"></i><br>
                                ${colaborador.cursosAVencer > 0 ? `<span class="badge" style="background-color: var(--sicoob-amarelo); color: var(--sicoob-verde-escuro);">${colaborador.cursosAVencer} a vencer</span><br>` : ''}
                                ${colaborador.cursosVencidos > 0 ? `<span class="badge" style="background-color: var(--sicoob-vermelho); color: white;">${colaborador.cursosVencidos} vencidos</span>` : ''}
                            </div>
                        </div>
                    </div>
                `).join('');

                botao.disabled = false;
                textoBotao.textContent = `Enviar para ${colaboradoresSelecionados.length} colaborador(es)`;
                cpfsInput.value = colaboradoresSelecionados.map(c => c.cpf).join(',');
            }
        }

            // === FUNCIONALIDADES DE TEMPLATES ===
            window.novoTemplate = function() {
                document.getElementById('templateEditorModalLabel').innerHTML = '<i class="fas fa-plus me-2"></i>Novo Template';
                document.getElementById('template_id_edit').value = '';
                document.getElementById('formTemplate').reset();
                document.getElementById('ativo_template').checked = true;
                document.getElementById('preview_template').innerHTML = '<p class="text-muted">Digite o conteúdo HTML e clique em "Atualizar Preview" para ver o resultado.</p>';

                const modal = new bootstrap.Modal(document.getElementById('templateEditorModal'));
                modal.show();
            };

            window.editarTemplate = function(templateId) {
                document.getElementById('templateEditorModalLabel').innerHTML = '<i class="fas fa-edit me-2"></i>Editar Template';
                document.getElementById('template_id_edit').value = templateId;

                // Buscar dados do template
                fetch(`ajax_template.php?id=${templateId}&edit=1`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const template = data.template;
                            document.getElementById('nome_template').value = template.nome;
                            document.getElementById('tipo_template').value = template.tipo;
                            document.getElementById('assunto_template').value = template.assunto;
                            document.getElementById('corpo_html_template').value = template.corpo_html;
                            document.getElementById('corpo_texto_template').value = template.corpo_texto;
                            document.getElementById('ativo_template').checked = template.ativo == 1;

                            atualizarPreview();

                            const modal = new bootstrap.Modal(document.getElementById('templateEditorModal'));
                            modal.show();
                        } else {
                            alert('Erro ao carregar template: ' + data.error);
                        }
                    })
                    .catch(error => {
                        alert('Erro ao carregar template: ' + error.message);
                    });
            };

            window.inserirVariavel = function(variavel) {
                const textarea = document.getElementById('corpo_html_template');
                const cursorPos = textarea.selectionStart;
                const textoBefore = textarea.value.substring(0, cursorPos);
                const textoAfter = textarea.value.substring(cursorPos);

                textarea.value = textoBefore + '{{' + variavel + '}}' + textoAfter;
                textarea.focus();
                textarea.setSelectionRange(cursorPos + variavel.length + 4, cursorPos + variavel.length + 4);
            };

            window.atualizarPreview = function() {
                const conteudoHtml = document.getElementById('corpo_html_template').value;
                const preview = document.getElementById('preview_template');

                if (conteudoHtml.trim() === '') {
                    preview.innerHTML = '<p class="text-muted">Digite o conteúdo HTML para ver o preview.</p>';
                    return;
                }

            // Dados de exemplo para listas de cursos
            const listaAVencerHtml = `
                <div style="margin: 15px 0;">
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 10px; padding: 8px; border-left: 3px solid #ffc107; background-color: #f8f9fa;">
                            <div style="display: flex; justify-content: between; align-items: center;">
                                <div>
                                    <strong style="color: #333;">Trilha de Compliance</strong><br>
                                    <span style="color: #666; font-size: 0.9em;">Curso de Prevenção à Lavagem de Dinheiro</span>
                                </div>
                                <div style="text-align: right; color: #ffc107; font-size: 0.85em;">
                                    <i class="fas fa-clock"></i> 15/12/2024<br>
                                    <small>5 dia(s) restantes</small>
                                </div>
                            </div>
                        </li>
                        <li style="margin-bottom: 10px; padding: 8px; border-left: 3px solid #ffc107; background-color: #f8f9fa;">
                            <div style="display: flex; justify-content: between; align-items: center;">
                                <div>
                                    <strong style="color: #333;">Trilha de Atendimento</strong><br>
                                    <span style="color: #666; font-size: 0.9em;">Excelência no Atendimento ao Cliente</span>
                                </div>
                                <div style="text-align: right; color: #ffc107; font-size: 0.85em;">
                                    <i class="fas fa-clock"></i> 20/12/2024<br>
                                    <small>10 dia(s) restantes</small>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            `;

            const listaVencidosHtml = `
                <div style="margin: 15px 0;">
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 10px; padding: 8px; border-left: 3px solid #dc3545; background-color: #f8f9fa;">
                            <div style="display: flex; justify-content: between; align-items: center;">
                                <div>
                                    <strong style="color: #333;">Trilha de Segurança</strong><br>
                                    <span style="color: #666; font-size: 0.9em;">Segurança da Informação</span>
                                </div>
                                <div style="text-align: right; color: #dc3545; font-size: 0.85em;">
                                    <i class="fas fa-exclamation-triangle"></i> 01/12/2024<br>
                                    <small>9 dia(s) em atraso</small>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            `;

            const listaAVencerTexto = `1. Trilha de Compliance - Curso de Prevenção à Lavagem de Dinheiro
   Prazo: 15/12/2024 (5 dia(s) restantes)

2. Trilha de Atendimento - Excelência no Atendimento ao Cliente
   Prazo: 20/12/2024 (10 dia(s) restantes)`;

            const listaVencidosTexto = `1. Trilha de Segurança - Segurança da Informação
   Prazo: 01/12/2024 (9 dia(s) em atraso)`;

            // Substituir variáveis por dados de exemplo
            let conteudoComExemplo = conteudoHtml
                .replace(/\{\{nome_colaborador\}\}/g, 'João Silva Santos')
                .replace(/\{\{cpf_colaborador\}\}/g, '123.456.789-01')
                .replace(/\{\{email_colaborador\}\}/g, '<EMAIL>')
                .replace(/\{\{funcao_colaborador\}\}/g, 'Gerente de Relacionamento')
                .replace(/\{\{pa_colaborador\}\}/g, '3049 - PA EXEMPLO')
                .replace(/\{\{total_cursos_a_vencer\}\}/g, '2')
                .replace(/\{\{total_cursos_vencidos\}\}/g, '1')
                .replace(/\{\{lista_cursos_a_vencer\}\}/g, listaAVencerHtml)
                .replace(/\{\{lista_cursos_vencidos\}\}/g, listaVencidosHtml)
                .replace(/\{\{lista_cursos_a_vencer_texto\}\}/g, listaAVencerTexto)
                .replace(/\{\{lista_cursos_vencidos_texto\}\}/g, listaVencidosTexto)
                .replace(/\{\{data_envio\}\}/g, new Date().toLocaleString('pt-BR'));

            preview.innerHTML = conteudoComExemplo;
        }

        // Verificar se deve ativar a aba histórico
        const urlParams = new URLSearchParams(window.location.search);
        const filtroHistorico = urlParams.get('filtro_historico');
        const hash = window.location.hash;

        if (filtroHistorico || hash === '#historico') {
            // Ativar aba histórico
            const historicoTab = document.getElementById('historico-tab');
            const historicoPane = document.getElementById('historico');

            if (historicoTab && historicoPane) {
                // Desativar aba ativa atual
                document.querySelectorAll('.nav-link.active').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelectorAll('.tab-pane.active').forEach(pane => {
                    pane.classList.remove('active', 'show');
                });

                // Ativar aba histórico
                historicoTab.classList.add('active');
                historicoPane.classList.add('active', 'show');
            }
        }

        }); // Fim do DOMContentLoaded
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
