<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'edu_auth_check.php';

echo "<h2>Teste da Funcionalidade de Envio de Email do Modal</h2>";

// Testar se os arquivos AJAX existem e são acessíveis
$arquivos_teste = [
    'ajax_templates_list.php' => 'Lista de Templates',
    'ajax_template_preview.php' => 'Preview de Template'
];

echo "<h3>1. Verificação de Arquivos AJAX</h3>";
foreach ($arquivos_teste as $arquivo => $descricao) {
    if (file_exists($arquivo)) {
        echo "<p>✅ <strong>$descricao</strong> ($arquivo) - Arquivo existe</p>";
    } else {
        echo "<p>❌ <strong>$descricao</strong> ($arquivo) - Arquivo não encontrado</p>";
    }
}

// Testar conexão com banco de dados
echo "<h3>2. Verificação de Banco de Dados</h3>";
try {
    $stmt = $pdo_edu->query("SELECT COUNT(*) as total FROM edu_email_templates WHERE ativo = 1");
    $total_templates = $stmt->fetch()['total'];
    echo "<p>✅ <strong>Templates Ativos:</strong> $total_templates templates encontrados</p>";
} catch (Exception $e) {
    echo "<p>❌ <strong>Erro no banco:</strong> " . $e->getMessage() . "</p>";
}

// Testar se existe pelo menos um colaborador para teste
echo "<h3>3. Verificação de Dados de Teste</h3>";
try {
    $stmt = $pdo_edu->query("SELECT cpf, usuario, email FROM edu_relatorio_educacao LIMIT 1");
    $colaborador_teste = $stmt->fetch();
    if ($colaborador_teste) {
        echo "<p>✅ <strong>Colaborador de Teste:</strong> {$colaborador_teste['usuario']} (CPF: {$colaborador_teste['cpf']})</p>";
        $cpf_teste = $colaborador_teste['cpf'];
    } else {
        echo "<p>❌ <strong>Nenhum colaborador encontrado para teste</strong></p>";
        $cpf_teste = null;
    }
} catch (Exception $e) {
    echo "<p>❌ <strong>Erro ao buscar colaborador:</strong> " . $e->getMessage() . "</p>";
    $cpf_teste = null;
}

// Testar arquivo de dados do colaborador
$arquivos_teste['ajax_colaborador_dados.php'] = 'Dados do Colaborador';
if (file_exists('ajax_colaborador_dados.php')) {
    echo "<p>✅ <strong>Dados do Colaborador</strong> (ajax_colaborador_dados.php) - Arquivo existe</p>";
    if ($cpf_teste) {
        $url_dados = "ajax_colaborador_dados.php?cpf=$cpf_teste";
        echo "<p>🔗 <a href='$url_dados' target='_blank'>Testar Dados do Colaborador</a></p>";
    }
} else {
    echo "<p>❌ <strong>Dados do Colaborador</strong> (ajax_colaborador_dados.php) - Arquivo não encontrado</p>";
}

// Testar AJAX de templates
echo "<h3>4. Teste do AJAX de Templates</h3>";
if (file_exists('ajax_templates_list.php')) {
    $url_templates = 'ajax_templates_list.php';
    echo "<p>🔗 <a href='$url_templates' target='_blank'>Testar Lista de Templates</a></p>";
} else {
    echo "<p>❌ Arquivo ajax_templates_list.php não encontrado</p>";
}

// Testar AJAX de preview (se temos dados de teste)
echo "<h3>5. Teste do AJAX de Preview</h3>";
if (file_exists('ajax_template_preview.php') && $cpf_teste && $total_templates > 0) {
    // Buscar um template para teste
    $stmt = $pdo_edu->query("SELECT id FROM edu_email_templates WHERE ativo = 1 LIMIT 1");
    $template_teste = $stmt->fetch();
    if ($template_teste) {
        $url_preview = "ajax_template_preview.php?template_id={$template_teste['id']}&cpf=$cpf_teste";
        echo "<p>🔗 <a href='$url_preview' target='_blank'>Testar Preview de Template</a></p>";
    } else {
        echo "<p>❌ Nenhum template ativo encontrado para teste</p>";
    }
} else {
    echo "<p>❌ Não é possível testar preview (arquivo não encontrado ou dados insuficientes)</p>";
}

// Testar se a ação foi adicionada corretamente no emails.php
echo "<h3>6. Verificação da Ação no emails.php</h3>";
$emails_content = file_get_contents('emails.php');
if (strpos($emails_content, 'envio_individual_modal') !== false) {
    echo "<p>✅ <strong>Ação 'envio_individual_modal' encontrada no emails.php</strong></p>";
} else {
    echo "<p>❌ <strong>Ação 'envio_individual_modal' NÃO encontrada no emails.php</strong></p>";
}

// Instruções para teste manual
echo "<h3>7. Instruções para Teste Manual</h3>";
echo "<div class='alert alert-info'>";
echo "<strong>⚠️ CORREÇÃO IMPLEMENTADA:</strong> O JavaScript e modal foram movidos para a página principal analise_colaboradores.php para resolver o erro 'abrirModalEnvioEmail is not defined'.";
echo "</div>";
echo "<ol>";
echo "<li>Acesse a página <a href='analise_colaboradores.php?aba=colaboradores' target='_blank'>Análise de Colaboradores</a></li>";
echo "<li>Clique em 'Ver Detalhes' de qualquer colaborador</li>";
echo "<li>No modal que abrir, procure pelo botão 'Enviar Email' no canto superior direito</li>";
echo "<li>Clique no botão 'Enviar Email'</li>";
echo "<li>Verifique se o modal de envio de email abre corretamente</li>";
echo "<li>Verifique se a lista de templates é carregada</li>";
echo "<li>Selecione um template e verifique se o preview é exibido</li>";
echo "<li>Clique em 'Enviar Email' para testar o envio</li>";
echo "</ol>";

echo "<h3>8. Arquivos Modificados</h3>";
echo "<ul>";
echo "<li><strong>analise_colaboradores.php:</strong> Adicionado modal e JavaScript para envio de email</li>";
echo "<li><strong>detalhes_colaborador.php:</strong> Mantido apenas o botão, removido modal e JavaScript</li>";
echo "<li><strong>emails.php:</strong> Adicionada ação 'envio_individual_modal'</li>";
echo "<li><strong>ajax_templates_list.php:</strong> Lista templates disponíveis</li>";
echo "<li><strong>ajax_template_preview.php:</strong> Gera preview do template</li>";
echo "<li><strong>ajax_colaborador_dados.php:</strong> Busca dados do colaborador</li>";
echo "</ul>";

echo "<h3>9. Verificação de Permissões</h3>";
$user_level = $_SESSION['user_level'] ?? 'N/A';
echo "<p><strong>Nível do usuário atual:</strong> $user_level</p>";
if ($user_level >= 2) {
    echo "<p>✅ <strong>Usuário tem permissão para enviar emails (nível $user_level)</strong></p>";
} else {
    echo "<p>⚠️ <strong>Usuário pode não ter permissão para enviar emails (nível $user_level)</strong></p>";
}

echo "<hr>";
echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>👥 Testar Modal</a>";
echo "<a href='emails.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>📧 Página de Emails</a>";
echo "</p>";
?>
