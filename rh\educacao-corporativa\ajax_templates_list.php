<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'edu_auth_check.php';

header('Content-Type: application/json');

try {
    // Buscar templates ativos
    $stmt = $pdo_edu->prepare("
        SELECT id, nome, tipo, assunto
        FROM edu_email_templates 
        WHERE ativo = 1 
        ORDER BY tipo, nome
    ");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'templates' => $templates
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
